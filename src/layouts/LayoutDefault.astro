---
import '~/styles/index.css'
// https://sivan.github.io/heti
import '~/styles/heti.min.css'
import Header from '~/components/Header.astro'
import Footer from '~/components/Footer.astro'
import { SEO } from 'astro-seo'
import { getImage } from 'astro:assets'

interface Props {
  title?: string
  desc?: string
  banner?: ImageMetadata
}

const props = Astro.props
const themeConfig = Astro.locals.config
const { header } = Astro.locals.config

const title = props.title ?? themeConfig.title
const desc = props.desc ?? themeConfig.desc
const canonical = themeConfig.website

const image =
  props.banner &&
  (await getImage({
    src: props.banner,
    format: "jpeg"
  }))
const optimizedImage = new URL(image?.src??"/placeholder.png", Astro.url).toString()
---

<html lang="en">
  <head>
    <meta name="viewport" content="width=device-width" />
    <meta name="generator" content={Astro.generator} />
    <script is:inline async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-675*************"
     crossorigin="anonymous"></script>
    <SEO
      charset="utf-8"
      title={title}
      description={desc}
      extend={{
        link: [{ rel: 'icon', href: '/mountains.png', type: 'image/png' }],
        meta: [
          { name: "twitter:image:src", content: optimizedImage },
          { name: "twitter:image:alt", content: desc },
          { name: "twitter:creator", content: header.twitter },
          { name: "twitter:site", content: header.twitter },
          { name: "twitter:card", content: "summary_large_image" },
          { name: "twitter:title", content: title },
          { name: "twitter:description", content: desc },
        ],
      }}
      openGraph={{
        basic: {
          title: title,
          type: "article",
          image: optimizedImage,
          url: canonical,
        }
      }}
    />
  </head>
  <body>
    <div un-max-lg="flex flex-col gap-0" un-lg="flex flex-row-reverse justify-center gap-4 px-8" class="container contain-layout m-a">
      <div un-lg="sticky top-0 h-screen py-20 animate-fadein-left gap-8 min-h-screen" un-lg:flex="~ col justify-between items-start gap-4" class="px-7.5 pt-1 pb-0" un-max-lg="pt-0">
        <Header />
        <Footer class="max-lg:hidden" />
      </div>
      <main un-lg="py-20 w-3xl animate-fadein-down" class="px-7.5 pt-0 pb-3" un-max-lg="pb-1">
        <slot />
      </main>
      <Footer class="lg:hidden p-7.5" un-max-lg="p-0.5" />
    </div>
  </body>
</html>

<style>
  /* 确保导航栏位置一致性 */
  @media (min-width: 1024px) {
    .container {
      min-height: 100vh;
    }

    /* 确保sticky定位的一致性 */
    [un-lg*="sticky"] {
      position: sticky !important;
      top: 0 !important;
    }
  }
</style>
