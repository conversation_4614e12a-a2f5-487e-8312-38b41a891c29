---
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import Post from '~/components/Post.astro'
import Pagination from '~/components/Pagination.astro'
import { getPosts, getPostDescription } from '~/utils'
import type { InferGetStaticPropsType, GetStaticPaths } from 'astro'

const { translate: t } = Astro.locals

export const getStaticPaths = (async ({ paginate }) => {
  const posts = await getPosts()
  const { pagination } = await import('~/theme.config').then(m => m.THEME_CONFIG)
  return paginate(posts, { pageSize: pagination.postsPerPage })
}) satisfies GetStaticPaths

type Props = InferGetStaticPropsType<typeof getStaticPaths>

const { page } = Astro.props
---

<LayoutDefault>
  <section contain-layout un-flex="~ col gap-7.5" un-max-lg="gap-2" class="first-post-mobile">
    {
      page.data.map((post, index) => (
        <Post post={post} class={index === 0 ? 'first-post' : ''}>
          <p class="line-clamp-4">{getPostDescription(post)}</p>
        </Post>
      ))
    }

    <Pagination
      showLeft={Boolean(page.url.prev)}
      leftTitle={t('prev')}
      leftUrl={page.url.prev}
      showRight={Boolean(page.url.next)}
      rightTitle={t('next')}
      rightUrl={page.url.next}
      currentPage={page.currentPage}
      totalPage={page.lastPage}
      baseUrl="/posts/page/"
    />
  </section>
</LayoutDefault>
