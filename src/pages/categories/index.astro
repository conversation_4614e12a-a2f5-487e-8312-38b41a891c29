---
import ListItem from '~/components/ListItem.astro'
import ListSection from '~/components/ListSection.astro'
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import { getCategories, getPathFromCategory } from '~/utils/index'

const { translate: t } = Astro.locals
const { category_map } = Astro.locals.config;

const categories = await getCategories()
---

<LayoutDefault>
  <ListSection>
    {Array.from(categories).map(([key, value]) => <ListItem title={key} href={`/categories/${getPathFromCategory(key, category_map)}`} description={t('categories_count', value.length)} />)}
  </ListSection>
</LayoutDefault>
