---
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import ListSection from '~/components/ListSection.astro'
import { getCategories, formatDate, getPathFromCategory } from '~/utils'
import ListItem from '~/components/ListItem.astro'
import { THEME_CONFIG } from '~/theme.config'
import type { Post } from '~/types/index'

export async function getStaticPaths() {
  try {
    const categories = await getCategories()
    return Array.from(categories).map(([key, value]) => {
      const path = getPathFromCategory(key, THEME_CONFIG.category_map);
      return {
        params: { category: path, name: key },
        props: { posts: value },
      }
    })
  } catch (error) {
    console.error('Error generating category paths:', error);
    return []
  }
}

const { posts } = Astro.props
const { name } = Astro.params

---

<LayoutDefault>
  <ListSection title={name}>
    {posts.map((post: Post) => <ListItem title={post.data.title} href={`/posts/${post.slug}/`} description={formatDate(post.data.pubDate)} />)}
  </ListSection>
</LayoutDefault>