---
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import PhotoGrid from '~/components/PhotoGrid.astro'
import { getCollection } from 'astro:content'

// 获取所有照片
const allPhotos = await getCollection('photos')

// 分页设置
const PHOTOS_PER_PAGE = 10
const currentPage = 1
const totalPhotos = allPhotos.length
const totalPages = Math.ceil(totalPhotos / PHOTOS_PER_PAGE)

// 按日期排序（最新的在前）
const sortedPhotos = allPhotos.sort((a, b) =>
  new Date(b.data.date).getTime() - new Date(a.data.date).getTime()
)

// 获取当前页的照片
const startIndex = (currentPage - 1) * PHOTOS_PER_PAGE
const endIndex = startIndex + PHOTOS_PER_PAGE
const photos = sortedPhotos.slice(startIndex, endIndex)

const title = "Gallery"
const description = "A collection of my photography work"
---

<LayoutDefault title={title} desc={description}>
  <div class="gallery-page">
    <PhotoGrid
      photos={photos}
      currentPage={currentPage}
      totalPages={totalPages}
    />
  </div>
</LayoutDefault>

<style>
  .gallery-page {
    @apply min-h-screen;
  }

  /* 添加淡入动画 */
  .gallery-page {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
