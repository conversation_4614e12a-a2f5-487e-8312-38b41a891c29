---
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import EssayCard from '~/components/EssayCard.astro'
import Pagination from '~/components/Pagination.astro'
import { getEssays } from '~/utils'
import type { InferGetStaticPropsType, GetStaticPaths } from 'astro'
import '~/styles/essay-timeline.css'

const { translate: t } = Astro.locals

export const getStaticPaths = (async ({ paginate }) => {
  const essays = await getEssays()
  const { pagination } = await import('~/theme.config').then(m => m.THEME_CONFIG)
  return paginate(essays, { pageSize: pagination.essaysPerPage })
}) satisfies GetStaticPaths

type Props = InferGetStaticPropsType<typeof getStaticPaths>

const { page } = Astro.props
---

<LayoutDefault>
  {/* 使用与主页相同的布局 */}
  <section contain-layout un-flex="~ col" un-max-lg="gap-2">
    {/* 随笔列表 - 直接显示内容 */}
    {
      page.data.map(async (essay) => {
        const { Content } = await essay.render()
        return (
          <EssayCard essay={essay}>
            <div class="prose prose-invert [&>p]:mb-0 [&>*]:text-gray-300 [&>*:last-child]:mb-0">
              <Content />
            </div>
          </EssayCard>
        )
      })
    }

    {/* 分页导航 */}
    <Pagination
      showLeft={Boolean(page.url.prev)}
      leftTitle={t('prev')}
      leftUrl={page.url.prev}
      showRight={Boolean(page.url.next)}
      rightTitle={t('next')}
      rightUrl={page.url.next}
      currentPage={page.currentPage}
      totalPage={page.lastPage}
      baseUrl="/essays/page/"
      showPageCount={false}
    />
  </section>
</LayoutDefault>
