---
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import EssayCard from '~/components/EssayCard.astro'
import Pagination from '~/components/Pagination.astro'
import { getEssays } from '~/utils'
import '~/styles/essay-timeline.css'

const { translate: t } = Astro.locals

// 获取随笔用于首页显示
const { pagination } = Astro.locals.config
const allEssays = await getEssays()
const essays = allEssays.slice(0, pagination.essaysPerPage)
const totalPages = Math.ceil(allEssays.length / pagination.essaysPerPage)
---

<LayoutDefault>
  <section contain-layout un-flex="~ col">
    {
      essays.map(async (essay) => {
        const { Content } = await essay.render()
        return (
          <EssayCard essay={essay}>
            <div class="prose prose-invert [&>p]:mb-0 [&>*]:text-gray-300 [&>*:last-child]:mb-0">
              <Content />
            </div>
          </EssayCard>
        )
      })
    }
    {totalPages > 1 && (
      <Pagination
        showLeft={false}
        showRight={true}
        rightTitle={t('next')}
        rightUrl={'essays/page/2'}
        currentPage={1}
        totalPage={totalPages}
        baseUrl="/essays/page/"
        showPageCount={false}
      />
    )}
  </section>
</LayoutDefault>
