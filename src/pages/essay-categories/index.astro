---
import ListItem from '~/components/ListItem.astro'
import ListSection from '~/components/ListSection.astro'
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import { getEssayCategories, getPathFromCategory } from '~/utils/index'


const { category_map } = Astro.locals.config;

const categories = await getEssayCategories()
---

<LayoutDefault>
  <ListSection title="随笔分类">
    {Array.from(categories).map(([key, value]) => <ListItem title={key} href={`/essay-categories/${getPathFromCategory(key, category_map)}`} description={`${value.length} 篇随笔`} />)}
  </ListSection>
</LayoutDefault>
