---
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import ListSection from '~/components/ListSection.astro'
import { getEssayCategories, formatDate, getPathFromCategory, getEssayDisplayTitle } from '~/utils'
import ListItem from '~/components/ListItem.astro'
import { THEME_CONFIG } from '~/theme.config'
import type { Essay } from '~/types/index'

export async function getStaticPaths() {
  try {
    const categories = await getEssayCategories()
    return Array.from(categories).map(([key, value]) => {
      const path = getPathFromCategory(key, THEME_CONFIG.category_map);
      return {
        params: { category: path, name: key },
        props: { essays: value },
      }
    })
  } catch (error) {
    console.error('Error generating essay category paths:', error);
    return []
  }
}

const { essays } = Astro.props
const { name } = Astro.params

---

<LayoutDefault>
  <ListSection title={name}>
    {essays.map((essay: Essay) => <ListItem title={essay.data.title || getEssayDisplayTitle(essay)} href={`/essays/${essay.slug}/`} description={formatDate(essay.data.pubDate)} />)}
  </ListSection>
</LayoutDefault>
