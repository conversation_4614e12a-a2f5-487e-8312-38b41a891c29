---
import LayoutDefault from '~/layouts/LayoutDefault.astro'
import ListSection from '~/components/ListSection.astro'
import ListItem from '~/components/ListItem.astro'
import { getPosts, formatDate } from '~/utils'
import type { Post } from '~/types/index'

const posts = await getPosts()

const postByYear = new Map<number, Post[]>()
posts.forEach((post: Post) => {
  const pubDate = post.data.pubDate ?? new Date()
  const year = pubDate.getFullYear()
  if (!postByYear.has(year)) {
    postByYear.set(year, [])
  }
  postByYear.get(year)!.push(post)
})

---

<LayoutDefault>
  <div un-flex="~ col gap-4">
    {
      Array.from(postByYear.entries()).map(([year, posts]) => (
        <ListSection title={year}>
          {posts.map((post) => (
            <ListItem title={post.data.title} href={`/posts/${post.slug}/`} description={formatDate(post.data.pubDate)} />
          ))}
        </ListSection>
      ))
    }
  </div>
</LayoutDefault>
