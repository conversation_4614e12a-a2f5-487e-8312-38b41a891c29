---
import type { CollectionEntry } from 'astro:content'
import { formatEssayDate } from '~/utils'

interface Props {
  essay: CollectionEntry<'essays'>
}

const { essay } = Astro.props
const { author } = Astro.locals.config
---

<!-- 带连接线的随笔卡片 -->
<article class="relative">
  <!-- 圆点和内容 -->
  <div class="relative flex gap-3 py-6">
    <div class="flex-shrink-0 relative">
      <!-- 连接线 - 穿过圆点中心连接上下 -->
      <div class="absolute left-2 -top-6 h-8 w-px bg-gray-600 essay-line-top"></div>
      <div class="absolute left-2 top-2 -bottom-6 w-px bg-gray-600 essay-line-bottom"></div>

      <!-- 圆点 -->
      <div class="w-4 h-4 rounded-full bg-gray-500 relative z-10"></div>
    </div>

    <!-- 内容区域 -->
    <div class="flex-1 min-w-0 pb-4" un-max-lg="pb-2">
      <!-- 用户名和时间 -->
      <div class="flex items-center gap-2 mb-2">
        <span class="text-white font-medium text-sm">{author}</span>
        <span class="text-gray-500 text-xs">{formatEssayDate(essay.data.pubDate)}</span>
      </div>

      <!-- 随笔内容 - 增大字体 -->
      <div class="text-gray-300 text-base leading-relaxed">
        <slot />
      </div>
    </div>
  </div>
</article>
