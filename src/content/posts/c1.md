---
title: 对 C++中引用（&）的理解
categories: ["C++"]
pubDate: 2024-2-5
description: ''
---

  之前在编写需要修改指针内容的函数时，一般采用双指针的写法，即函数参数中写"***\*p**"，然后用"**&p**"将存储p指针的地址输入进去，这样的方法易于理解，但是在程序上会显得非常麻烦，易读性不强。  

  而408王道数据结构中的示例代码也是采用引用（&）的方式，故metoo. 

## 一.引用的概念

  "***引用不是新定义一个变量，而是给已存在变量取了一个别名，编译器不会为引用变量开辟内存空间，它和它引用的变量共用同一块内存空间**"*

  定义略显模糊，引用如果是给变量取了一个别名，那为什么可以向函数中传递实参？（√） 

## 二.在函数参数中的使用

```cpp
#include <stdio.h>

void swap(int& left,int& right)
{
    int tmp = left;
    left = right;
    right = tmp;
}
int main()
{   
    int left=1;
    int right=666;
    printf("before swap left: %d right : %d\n",left,right);
    swap(left,right);
    printf("after swap left: %d right : %d",left,right);
    return 0;
}
```

如上代码，**在传参时也就是说执行了** `int& left = left;int& right = right;`**所以说swap函数中的left相当于是main函数中的left的一个别名**，两者所用的内存以及值都是相同的！！！悟了

## 三.引用的注意事项（与指针的不同）

- ***不存在空引用。引用必须连接到一块合法的内存。***
- ***一旦引用被初始化为一个对象，就不能被指向到另一个对象。指针可以在任何时候指向到另一个对象。***
- ***引用必须在创建时被初始化。指针可以在任何时间被初始化。***

 
