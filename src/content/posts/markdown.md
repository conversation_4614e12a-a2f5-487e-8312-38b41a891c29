---
title: markdown语法规则
categories: ["Web"]
pubDate: 2024-1-7
description: ''
---
Markdown 是一种轻量级标记语言，它允许人们使用易读易写的纯文本格式编写文档。Markdown 文档可以转换为 HTML 或其他格式。以下是一些基本的 Markdown 语法：

1. **标题**：使用 `#` 符号创建标题。一个 `#` 代表最大的标题，随着 `#` 数量的增加，标题的大小逐渐减小。例如：
   - `# 标题 1`
   - `## 标题 2`
   - `### 标题 3`

2. **粗体**：使用两个星号 `**` 包围文本来创建粗体文本。例如：`**粗体文本**`。

3. **斜体**：使用一个星号 `*` 或下划线 `_` 包围文本来创建斜体文本。例如：`*斜体文本*` 或 `_斜体文本_`。

4. **链接**：使用 `[链接文本](URL)` 来创建链接。例如：`[谷歌](https://www.google.com)`。

5. **图片**：使用 `![替代文本](图片URL)` 来插入图片。例如：`![这是一张图片](https://example.com/image.jpg)`。也可以使用`<img src="" />`，注意后面的斜杠要加上。

6. **列表**：
   - 无序列表：使用星号 `*`、加号 `+` 或减号 `-` 来创建。例如：
     ```
     * 列表项一
     * 列表项二
     * 列表项三
     ```
   - 有序列表：使用数字后跟点号。例如：
     ```
     1. 第一项
     2. 第二项
     3. 第三项
     ```

7. **引用**：使用 `>` 符号来创建引用文本。例如：`> 这是一个引用。`

8. **代码**：
   - 行内代码：使用一对反引号 `` ` `` 来包围代码。例如：`` `代码` ``。
   - 代码块：使用三个反引号 ``` 或四个空格来创建代码块。
