/*!
 * Project: Heti
 * URL: https://github.com/sivan/heti
 * Author: <PERSON><PERSON> [<EMAIL>]
 */
@font-face {
    font-family: "Heti Hei";
    src: "Heti Hei SC", "Heti Hei TC", "Heti Hei JP", "Heti Hei KR"
}

@font-face {
    font-family: "Heti Hei SC";
    src: local("PingFang SC Regular"), local("Heiti SC Regular"), local("Microsoft YaHei"), local("Source Han Sans CN Regular"), local("Noto Sans CJK SC Regular"), local("WenQuanYi Micro Hei"), local("Droid Sans Fallback")
}

@font-face {
    font-family: "Heti Hei TC";
    src: local("PingFang TC Regular"), local("Heiti TC Regular"), local("Microsoft Jhenghei"), local("Source Han Sans HK Regular"), local("Source Han Sans TW Regular"), local("Noto Sans CJK TC Regular"), local("WenQuanYi Micro Hei"), local("Droid Sans Fallback")
}

@font-face {
    font-family: "Heti Hei JP";
    src: local("Hiragino Sans GB W3"), local("Source Han Sans JP Regular"), local("Noto Sans CJK JP Regular"), local("Droid Sans Fallback")
}

@font-face {
    font-family: "Heti Hei KR";
    src: local("Source Han Sans KR Regular"), local("Noto Sans CJK KR Regular"), local("Droid Sans Fallback")
}

@font-face {
    font-family: "Heti Hei";
    font-weight: 200;
    src: "Heti Hei SC Light", "Heti Hei TC Light", "Heti Hei JP Light", "Heti Hei KR Light"
}

@font-face {
    font-family: "Heti Hei SC Light";
    font-weight: 200;
    src: local("PingFang SC Light"), local("Heiti SC Light"), "Heti Hei SC Light Fallback", local("Source Han Sans CN Light"), local("Noto Sans CJK SC Light")
}

@font-face {
    font-family: "Heti Hei TC Light";
    font-weight: 200;
    src: local("PingFang TC Light"), local("Heiti TC Light"), local("Microsoft Jhenghei Light"), local("Source Han Sans HK Light"), local("Source Han Sans TW Light"), local("Noto Sans CJK TC Light")
}

@font-face {
    font-family: "Heti Hei JP Light";
    font-weight: 200;
    src: local("Source Han Sans JP Light"), local("Noto Sans CJK JP Light")
}

@font-face {
    font-family: "Heti Hei KR Light";
    font-weight: 200;
    src: local("Source Han Sans KR Light"), local("Noto Sans CJK KR Light")
}

@font-face {
    font-family: "Heti Hei SC Light Fallback";
    font-weight: 200;
    src: local("Microsoft YaHei"), local("Droid Sans Fallback")
}

@font-face {
    font-family: "Heti Hei";
    font-weight: 600;
    src: "Heti Hei SC Bold", "Heti Hei TC Bold", "Heti Hei JP Bold", "Heti Hei KR Bold"
}

@font-face {
    font-family: "Heti Hei SC Bold";
    font-weight: 600;
    src: local("PingFang SC Medium"), local("Heiti SC Medium"), "Heti Hei SC Bold Fallback", local("Source Han Sans CN Bold"), local("Noto Sans CJK SC Bold")
}

@font-face {
    font-family: "Heti Hei TC Bold";
    font-weight: 600;
    src: local("PingFang TC Medium"), local("Heiti TC Medium"), local("Microsoft Jhenghei Bold"), local("Source Han Sans HK Bold"), local("Source Han Sans TW Bold"), local("Noto Sans CJK TC Bold")
}

@font-face {
    font-family: "Heti Hei JP Bold";
    font-weight: 600;
    src: local("Hiragino Sans GB W6"), local("Source Han Sans JP Bold"), local("Noto Sans CJK JP Bold")
}

@font-face {
    font-family: "Heti Hei KR Bold";
    font-weight: 600;
    src: local("Source Han Sans KR Bold"), local("Noto Sans CJK KR Bold")
}

@font-face {
    font-family: "Heti Hei SC Bold Fallback";
    font-weight: 600;
    src: local("Microsoft YaHei"), local("Droid Sans Fallback")
}

@font-face {
    font-family: "Heti Hei Black";
    font-weight: 800;
    src: "Heti Hei SC Black", "Heti Hei TC Black", "Heti Hei JP Black", "Heti Hei KR Black"
}

@font-face {
    font-family: "Heti Hei SC Black";
    font-weight: 800;
    src: local("Lantinghei SC Heavy"), local("PingFang SC Semibold"), local("Heiti SC Medium"), "Heti Hei SC Black Fallback", local("Source Han Sans CN Heavy"), local("Noto Sans CJK SC Heavy")
}

@font-face {
    font-family: "Heti Hei TC Black";
    font-weight: 800;
    src: local("Lantinghei TC Heavy"), local("PingFang TC Semibold"), local("Heiti TC Medium"), local("Microsoft Jhenghei Bold"), local("Source Han Sans HK Heavy"), local("Source Han Sans TW Heavy"), local("Noto Sans CJK TC Heavy")
}

@font-face {
    font-family: "Heti Hei JP Black";
    font-weight: 800;
    src: local("Hiragino Sans GB W6"), local("Source Han Sans JP Heavy"), local("Noto Sans CJK JP Heavy")
}

@font-face {
    font-family: "Heti Hei KR Black";
    font-weight: 800;
    src: local("Source Han Sans KR Heavy"), local("Noto Sans CJK KR Heavy")
}

@font-face {
    font-family: "Heti Hei SC Black Fallback";
    font-weight: 800;
    src: local("Microsoft YaHei"), local("Droid Sans Fallback")
}

@font-face {
    font-family: "Heti Song";
    src: local("Songti SC Regular"), local("Songti TC Regular"), local("SimSun")
}

@font-face {
    font-family: "Heti Song";
    font-weight: 200;
    src: local("Songti SC Light"), local("Songti TC Light"), "Heti Song Light Fallback"
}

@font-face {
    font-family: "Heti Song Light Fallback";
    font-weight: 200;
    src: local("SimSun")
}

@font-face {
    font-family: "Heti Song";
    font-weight: 600;
    src: local("Songti SC Bold"), local("Songti TC Bold"), "Heti Song Bold Fallback"
}

@font-face {
    font-family: "Heti Song Bold Fallback";
    font-weight: 600;
    src: local("SimSun")
}

@font-face {
    font-family: "Heti Song Black";
    font-weight: 800;
    src: local("Songti SC Black"), local("SimSun")
}

@font-face {
    font-family: "Heti Kai";
    src: local("Kaiti SC Regular"), local("Kaiti TC Regular"), local("STKaiti"), local("Kaiti"), local("BiauKai")
}

@font-face {
    font-family: "Heti Kai";
    font-weight: 600;
    src: local("Kaiti SC Bold"), local("Kaiti TC Bold")
}

@font-face {
    font-family: "Heti Kai Bold Fallback";
    font-weight: 600;
    src: local("STKaiti"), local("Kaiti") local("BiauKai")
}

@font-face {
    font-family: "Heti Kai Black";
    font-weight: 800;
    src: local("Kaiti SC Black"), local("Kaiti TC Black"), local("STKaiti"), local("Kaiti")
}

.heti {
    max-width: 42em;
    font-size: 16px;
    font-weight: 400;
    -webkit-font-smoothing: subpixel-antialiased;
    line-height: 1.5;
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
    letter-spacing: .02em
}

.heti::before,
.heti::after {
    content: "";
    display: table
}

.heti::after {
    clear: both
}

.heti>*:first-child,
.heti section>*:first-child,
.heti td>*:first-child {
    margin-block-start: 0 !important
}

.heti>*:last-child,
.heti section>*:last-child,
.heti td>*:last-child {
    margin-block-end: 0 !important
}

.heti blockquote {
    margin-block-start: 12px;
    margin-block-end: 24px;
    margin-inline-start: 32px;
    margin-inline-end: 32px;
    padding-block-start: 12px;
    padding-block-end: 12px;
    padding-inline-start: 16px;
    padding-inline-end: 16px;
    background-color: rgba(0, 0, 0, .054)
}

@media(prefers-color-scheme: dark) {
    .heti blockquote {
        background-color: rgba(255, 255, 255, .054)
    }
}

.heti figure {
    display: block;
    text-align: center
}

.heti figure>img {
    display: block;
    margin-inline-start: auto;
    margin-inline-end: auto
}

.heti hr {
    width: 30%;
    height: 1px;
    margin-block-start: 48px;
    margin-block-end: 47px;
    margin-inline-start: auto;
    margin-inline-end: auto;
    border: 0;
    background-color: #ccc
}

@media(prefers-color-scheme: dark) {
    .heti hr {
        background-color: #404040
    }
}

.heti p {
    margin-block-start: 12px;
    margin-block-end: 24px;
    text-align: justify
}

.heti p:not(:lang(zh)):not(:lang(ja)):not(:lang(kr)),
.heti p:not(:lang(zh)) {
    text-align: start
}

.heti pre {
    margin-block-start: 12px;
    margin-block-end: 12px;
    margin-inline-start: 0;
    margin-inline-end: 0;
    padding-block-start: 12px;
    padding-block-end: 12px;
    padding-inline-start: 16px;
    padding-inline-end: 16px;
    overflow: auto;
    font-family: "SFMono-Regular", consolas, "Liberation Mono", menlo, courier, monospace, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    white-space: pre;
    word-wrap: normal;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, .054)
}

@media(prefers-color-scheme: dark) {
    .heti pre {
        background-color: rgba(255, 255, 255, .054)
    }
}

.heti pre code {
    margin: 0;
    padding: 0;
    border: 0;
    border-radius: 0;
    background-color: rgba(0, 0, 0, 0);
    color: inherit
}

.heti:not(:lang(zh)):not(:lang(ja)):not(:lang(kr)),
.heti:not(:lang(zh)) {
    letter-spacing: 0
}

.heti a,
.heti abbr,
.heti code,
.heti heti-spacing,
.heti [lang=en-US] {
    letter-spacing: normal
}

.heti h1,
.heti h2,
.heti h3,
.heti h4,
.heti h5,
.heti h6 {
    position: relative;
    margin: 0;
    margin-block-start: 24px;
    margin-block-end: 12px;
    font-weight: 600
}

.heti h1 {
    margin-block-end: 24px;
    font-size: 32px;
    line-height: 48px
}

.heti h2 {
    font-size: 24px;
    line-height: 36px
}

.heti h3 {
    font-size: 20px;
    line-height: 36px
}

.heti h4 {
    font-size: 18px;
    line-height: 24px
}

.heti h5 {
    font-size: 16px;
    line-height: 24px
}

.heti h6 {
    font-size: 14px;
    line-height: 24px
}

.heti h1,
.heti h2,
.heti h3 {
    letter-spacing: .05em
}

.heti h1:not(:lang(zh)):not(:lang(ja)):not(:lang(kr)),
.heti h1:not(:lang(zh)),
.heti h2:not(:lang(zh)):not(:lang(ja)):not(:lang(kr)),
.heti h2:not(:lang(zh)),
.heti h3:not(:lang(zh)):not(:lang(ja)):not(:lang(kr)),
.heti h3:not(:lang(zh)) {
    letter-spacing: 0
}

.heti h1+h2,
.heti h2+h3,
.heti h3+h4,
.heti h4+h5,
.heti h5+h6 {
    margin-block-start: 12px
}

.heti ul,
.heti ol,
.heti dl {
    margin-block-start: 12px;
    margin-block-end: 24px
}

.heti ul,
.heti ol {
    padding-inline-start: 32px
}

.heti ul ul,
.heti ul ol,
.heti ol ul,
.heti ol ol {
    margin-block-start: 0;
    margin-block-end: 0
}

.heti ul {
    list-style-type: disc
}

.heti ol {
    list-style-type: decimal
}

.heti ul ul,
.heti ol ul {
    list-style-type: circle
}

.heti ul ul ul,
.heti ul ol ul,
.heti ol ul ul,
.heti ol ol ul {
    list-style-type: square
}

.heti li {
    list-style-type: unset
}

.heti table {
    box-sizing: border-box;
    table-layout: fixed;
    margin-block-start: 12px;
    margin-block-end: 24px;
    margin-inline-start: auto;
    margin-inline-end: auto;
    border-collapse: collapse;
    border-width: 1px;
    border-style: solid;
    border-color: #ccc;
    word-break: break-word
}

@media(prefers-color-scheme: dark) {
    .heti table {
        border-color: #404040
    }
}

.heti th,
.heti td {
    padding-block-start: 6px;
    padding-block-end: 6px;
    padding-inline-start: 8px;
    padding-inline-end: 8px;
    border-width: 1px;
    border-style: solid;
    border-color: #ccc
}

@media(prefers-color-scheme: dark) {

    .heti th,
    .heti td {
        border-color: #404040
    }
}

.heti caption {
    caption-side: bottom;
    margin-block-start: 2px;
    margin-block-end: -4px;
    font-size: 14px;
    line-height: 24px
}

.heti a {
    text-decoration: none
}

.heti a:hover {
    padding-block-end: 1px;
    border-block-end: 1px solid currentColor;
    text-decoration: none
}

.heti abbr[title] {
    padding-block-end: 1px;
    border-block-end: 1px dotted;
    text-decoration: none;
    cursor: help
}

.heti b,
.heti strong {
    font-weight: 600
}

.heti code {
    margin-inline-start: .25em;
    margin-inline-end: .25em;
    font-family: "SFMono-Regular", consolas, "Liberation Mono", menlo, courier, monospace, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    font-size: .875em
}

.heti dfn {
    font-weight: 600
}

.heti dfn:not(:lang(zh)):not(:lang(ja)):not(:lang(kr)),
.heti dfn:not(:lang(zh)) {
    font-weight: 400
}

.heti em {
    font-weight: 600
}

.heti figcaption {
    display: inline-block;
    vertical-align: top;
    font-size: 14px;
    text-align: start
}

.heti i {
    font-style: italic
}

.heti ins,
.heti u {
    padding-block-end: 1px;
    border-block-end: 1px solid;
    text-decoration: none
}

.heti mark {
    padding-block-start: 2px;
    padding-block-end: 2px;
    padding-inline-start: 1px;
    padding-inline-end: 1px;
    margin-inline-start: 1px;
    margin-inline-end: 1px;
    background-color: rgba(255, 247, 0, .88);
    color: inherit
}

@media(prefers-color-scheme: dark) {
    .heti mark {
        background-color: rgba(77, 74, 0, .88)
    }
}

.heti q {
    quotes: "「" "」" "『" "』"
}

.heti q:not(:lang(zh)):not(:lang(ja)):not(:lang(kr)),
.heti q:not(:lang(zh)) {
    quotes: initial;
    quotes: auto
}

.heti rt {
    font-size: .875em;
    font-weight: 400
}

.heti small {
    font-size: .875em
}

.heti strong {
    font-weight: 600
}

.heti sub,
.heti sup {
    position: relative;
    margin-inline-start: .25em;
    margin-inline-end: .25em;
    font-size: .75em;
    font-family: "Helvetica Neue", helvetica, arial, "Heti Hei", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    font-style: normal;
    line-height: 1;
    vertical-align: baseline
}

.heti sub {
    bottom: -0.25em
}

.heti sup {
    top: -0.5em
}

.heti sup:target,
.heti sup a:target {
    background-color: #dbedff
}

@media(prefers-color-scheme: dark) {

    .heti sup:target,
    .heti sup a:target {
        background-color: #3a6188
    }
}

.heti summary {
    padding-inline-start: 1em;
    outline: 0;
    cursor: pointer
}

.heti summary::-webkit-details-marker {
    width: .6em;
    margin-inline-end: .4em
}

.heti u[title] {
    cursor: help;
    border-block-end-width: 3px;
    border-block-end-style: double;
    border-block-end-color: rgba(0, 0, 0, .54)
}

@media(prefers-color-scheme: dark) {
    .heti u[title] {
        border-block-end-color: rgba(255, 255, 255, .54)
    }
}

.heti address,
.heti cite,
.heti dfn,
.heti dt,
.heti em {
    font-style: normal
}

.heti address:not(:lang(zh)):not(:lang(ja)):not(:lang(kr)),
.heti address:not(:lang(zh)),
.heti cite:not(:lang(zh)):not(:lang(ja)):not(:lang(kr)),
.heti cite:not(:lang(zh)),
.heti dfn:not(:lang(zh)):not(:lang(ja)):not(:lang(kr)),
.heti dfn:not(:lang(zh)),
.heti dt:not(:lang(zh)):not(:lang(ja)):not(:lang(kr)),
.heti dt:not(:lang(zh)),
.heti em:not(:lang(zh)):not(:lang(ja)):not(:lang(kr)),
.heti em:not(:lang(zh)) {
    font-style: italic
}

.heti abbr[title],
.heti del,
.heti ins,
.heti s,
.heti u {
    margin-inline-start: 1px;
    margin-inline-end: 1px
}

.heti,
.heti--sans {
    font-family: "Helvetica Neue", helvetica, arial, "Heti Hei", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"
}

.heti--serif {
    font-family: "Times New Roman", times, "Heti Song", serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"
}

.heti--classic {
    font-family: "Times New Roman", times, "Heti Song", serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"
}

.heti--classic h1,
.heti--classic h2,
.heti--classic h3,
.heti--classic h4,
.heti--classic h5,
.heti--classic h6 {
    font-family: "Times New Roman", times, "Heti Kai Black", serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    font-weight: 800
}

.heti--classic blockquote,
.heti--classic cite,
.heti--classic q {
    font-family: "Times New Roman", times, "Heti Kai", serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"
}

.heti--classic figcaption,
.heti--classic caption,
.heti--classic th {
    font-family: "Helvetica Neue", helvetica, arial, "Heti Hei", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"
}

.heti--hei {
    font-family: "Helvetica Neue", helvetica, arial, "Heti Hei", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"
}

.heti--song {
    font-family: "Times New Roman", times, "Heti Song", serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"
}

.heti--kai {
    font-family: "Times New Roman", times, "Heti Kai", serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"
}

.heti--columns-1,
.heti--columns-2,
.heti--columns-3,
.heti--columns-4,
.heti--columns-16em,
.heti--columns-20em,
.heti--columns-24em,
.heti--columns-28em,
.heti--columns-32em,
.heti--columns-36em,
.heti--columns-40em,
.heti--columns-44em,
.heti--columns-48em,
.heti comma {
    max-width: none;
    column-gap: 2em
}

.heti--columns-1 p,
.heti--columns-2 p,
.heti--columns-3 p,
.heti--columns-4 p,
.heti--columns-16em p,
.heti--columns-20em p,
.heti--columns-24em p,
.heti--columns-28em p,
.heti--columns-32em p,
.heti--columns-36em p,
.heti--columns-40em p,
.heti--columns-44em p,
.heti--columns-48em p,
.heti comma p {
    margin-block-start: 6px;
    margin-block-end: 12px;
    text-indent: 2em
}

.heti--columns-1 {
    column-count: 1
}

.heti--columns-2 {
    column-count: 2
}

.heti--columns-3 {
    column-count: 3
}

.heti--columns-4 {
    column-count: 4
}

.heti--columns-16em {
    column-width: 16em
}

.heti--columns-20em {
    column-width: 20em
}

.heti--columns-24em {
    column-width: 24em
}

.heti--columns-28em {
    column-width: 28em
}

.heti--columns-32em {
    column-width: 32em
}

.heti--columns-36em {
    column-width: 36em
}

.heti--columns-40em {
    column-width: 40em
}

.heti--columns-44em {
    column-width: 44em
}

.heti--columns-48em {
    column-width: 48em
}

.heti--vertical {
    max-width: none;
    max-height: 42em;
    writing-mode: vertical-rl;
    letter-spacing: .125em
}

.heti--vertical h1,
.heti--vertical h2,
.heti--vertical h3,
.heti--vertical h4,
.heti--vertical h5,
.heti--vertical h6 {
    text-align: start
}

.heti--vertical q {
    quotes: "「" "」" "『" "』"
}

.heti--ancient,
.heti--poetry {
    font-family: "Times New Roman", times, "Heti Song", serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"
}

.heti--ancient h1,
.heti--ancient h2,
.heti--ancient h3,
.heti--ancient h4,
.heti--ancient h5,
.heti--ancient h6,
.heti--poetry h1,
.heti--poetry h2,
.heti--poetry h3,
.heti--poetry h4,
.heti--poetry h5,
.heti--poetry h6 {
    font-family: "Times New Roman", times, "Heti Kai Black", serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    font-weight: 800;
    text-align: center
}

.heti--ancient h1 .heti-meta,
.heti--ancient h2 .heti-meta,
.heti--ancient h3 .heti-meta,
.heti--ancient h4 .heti-meta,
.heti--ancient h5 .heti-meta,
.heti--ancient h6 .heti-meta,
.heti--poetry h1 .heti-meta,
.heti--poetry h2 .heti-meta,
.heti--poetry h3 .heti-meta,
.heti--poetry h4 .heti-meta,
.heti--poetry h5 .heti-meta,
.heti--poetry h6 .heti-meta {
    font-weight: 400
}

@media screen and (min-width: 640px) {

    .heti--ancient h1 .heti-meta,
    .heti--ancient h2 .heti-meta,
    .heti--ancient h3 .heti-meta,
    .heti--ancient h4 .heti-meta,
    .heti--ancient h5 .heti-meta,
    .heti--ancient h6 .heti-meta,
    .heti--poetry h1 .heti-meta,
    .heti--poetry h2 .heti-meta,
    .heti--poetry h3 .heti-meta,
    .heti--poetry h4 .heti-meta,
    .heti--poetry h5 .heti-meta,
    .heti--poetry h6 .heti-meta {
        position: absolute;
        line-height: inherit;
        text-indent: 0;
        display: inline;
        margin-block-start: 4px;
        margin-inline-start: 8px
    }
}

.heti--ancient .heti-meta,
.heti--poetry .heti-meta {
    line-height: 24px;
    text-align: center;
    text-indent: 0
}

.heti--ancient p {
    text-indent: 2em
}

.heti--poetry p {
    text-align: center;
    text-indent: 0
}

.heti--annotation p {
    margin-block-start: 0;
    margin-block-end: 0;
    line-height: 2.25;
    text-indent: 2em
}

.heti--annotation em {
    -webkit-text-emphasis: filled circle;
    -webkit-text-emphasis-position: under;
    text-emphasis: filled circle;
    text-emphasis-position: under right;
    font-weight: 400
}

.heti--annotation em:not(:lang(zh)):not(:lang(ja)):not(:lang(kr)),
.heti--annotation em:not(:lang(zh)) {
    -webkit-text-emphasis: none;
    text-emphasis: none
}

.heti--annotation .heti-meta {
    margin-block-start: 12px;
    margin-block-end: 24px
}

.heti .heti-meta {
    display: block;
    text-indent: 0
}

.heti .heti-verse {
    text-align: center;
    text-indent: 0
}

.heti .heti-large {
    font-size: 18px;
    line-height: 24px
}

.heti .heti-x-large {
    font-size: 20px;
    line-height: 30px;
    letter-spacing: .05em
}

.heti .heti-small {
    font-size: 14px;
    line-height: 24px
}

.heti .heti-x-small {
    font-size: 12px;
    line-height: 18px
}

.heti .heti-list-latin {
    list-style-type: upper-latin
}

.heti .heti-list-latin ol {
    list-style-type: lower-roman
}

.heti .heti-list-latin ol ol {
    list-style-type: lower-latin
}

.heti .heti-list-han {
    list-style-type: cjk-ideographic
}

.heti .heti-list-han ol {
    list-style-type: decimal
}

.heti .heti-list-han ol ol {
    list-style-type: decimal-leading-zero
}

.heti .heti-fn {
    margin-block-start: 59px;
    border-block-start: 1px solid;
    border-block-start-color: #ccc;
    font-size: 14px;
    font-family: "Helvetica Neue", helvetica, arial, "Heti Hei", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    line-height: 24px
}

@media(prefers-color-scheme: dark) {
    .heti .heti-fn {
        border-block-start-color: #404040
    }
}

.heti .heti-fn ol {
    margin-block-start: 12px;
    margin-block-end: 0
}

.heti .heti-fn li:target {
    background-color: #dbedff
}

@media(prefers-color-scheme: dark) {
    .heti .heti-fn li:target {
        background-color: #3a6188
    }
}

.heti .heti-hang {
    position: absolute;
    line-height: inherit;
    text-indent: 0
}

.heti .heti-em {
    -webkit-text-emphasis: filled circle;
    -webkit-text-emphasis-position: under;
    text-emphasis: filled circle;
    text-emphasis-position: under right
}

.heti .heti-em:not(:lang(zh)):not(:lang(ja)):not(:lang(kr)),
.heti .heti-em:not(:lang(zh)) {
    -webkit-text-emphasis: none;
    text-emphasis: none
}

.heti .heti-ruby--inline {
    display: inline-flex;
    flex-direction: column-reverse;
    height: 1.5em;
    vertical-align: top
}

.heti .heti-ruby--inline rt {
    display: inline;
    margin-bottom: -0.25em;
    line-height: 1;
    text-align: center
}

.heti heti-spacing {
    display: inline
}

.heti heti-spacing+sup,
.heti heti-spacing+sub {
    margin-inline-start: 0
}

.heti .heti-spacing-start {
    margin-inline-end: .25em
}

.heti .heti-spacing-end {
    margin-inline-start: .25em
}

.heti heti-adjacent {
    display: inline
}

.heti .heti-adjacent-half {
    margin-inline-end: -0.5em
}

.heti .heti-adjacent-quarter {
    margin-inline-end: -0.25em
}